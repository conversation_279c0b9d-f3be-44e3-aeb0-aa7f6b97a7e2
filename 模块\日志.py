import logging
import requests
import json
import threading
import time
from typing import Dict, Any, Optional
from datetime import datetime

class 日志记录服务客户端:
    """日志记录服务2.0简化版客户端"""

    def __init__(self, url: str = 'http://222.186.21.133:8742/日志记录服务.php', timeout: int = 10):
        self.url = url
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json; charset=utf-8',
            'User-Agent': 'SteamVault-Pro/1.0'
        })

    def send_log(self, level: str, message: str, extra: Dict[str, Any] = None,
                 user_code: str = None, encrypt: bool = False) -> bool:
        """
        发送日志到远程服务

        Args:
            level: 日志级别 (debug, info, warning, error, critical)
            message: 日志消息
            extra: 附加数据
            user_code: 用户激活码（如果用户已登录）
            encrypt: 是否加密传输

        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建日志消息，如果有用户激活码则添加前缀
            if user_code:
                formatted_message = f"[用户:{user_code[:12]}***] {message}"
            else:
                formatted_message = message

            data = {
                'level': level,
                'message': formatted_message,
                'extra': extra or {}
            }

            if encrypt:
                # 这里可以添加加密逻辑，暂时使用明文传输
                pass

            response = self.session.post(
                self.url,
                json=data,
                timeout=self.timeout
            )

            return response.status_code == 200

        except Exception as e:
            # 静默失败，不影响主程序运行
            return False

    def log_async(self, level: str, message: str, extra: Dict[str, Any] = None,
                  user_code: str = None) -> None:
        """异步发送日志"""
        def _send():
            self.send_log(level, message, extra, user_code)

        thread = threading.Thread(target=_send, daemon=True)
        thread.start()

# 全局日志服务客户端实例
_log_service_client = 日志记录服务客户端()

def setup_logger():
    logger = logging.getLogger('游戏列表应用')
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

def send_remote_log(level: str, message: str, extra: Dict[str, Any] = None,
                   user_code: str = None, async_send: bool = True) -> None:
    """
    发送日志到远程服务的便捷函数

    Args:
        level: 日志级别 (debug, info, warning, error, critical)
        message: 日志消息
        extra: 附加数据
        user_code: 用户激活码（如果用户已登录）
        async_send: 是否异步发送
    """
    if async_send:
        _log_service_client.log_async(level, message, extra, user_code)
    else:
        _log_service_client.send_log(level, message, extra, user_code)

def get_current_user_code() -> Optional[str]:
    """获取当前登录用户的激活码"""
    try:
        from main import auth_manager
        auth_client = auth_manager.get_client()
        if auth_client and auth_client.is_authenticated():
            return auth_client.auth_code
    except:
        pass
    return None

logger = setup_logger()